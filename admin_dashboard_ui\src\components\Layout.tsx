import React from "react";
import { Sidebar } from "./Sidebar";
import { Outlet } from "react-router-dom";
import { useSidebarStore } from "../hooks/useSidebarStore";
import { useThemeStore } from "../hooks/useThemeStore";
import { But<PERSON> } from "@/components/ui/button";
import { Sun, Moon } from "lucide-react";
import { UserProfile } from "./UserProfile";
import { GlobalDateFilter } from "./GlobalDateFilter";

export const Layout = () => {
  const { sidebarOpen } = useSidebarStore();
  const { theme, toggleTheme } = useThemeStore();

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-950 dark:via-gray-900 dark:to-gray-800 text-foreground">
      <Sidebar />
      <div className={`flex-1 flex flex-col transition-all duration-500 ease-out ${sidebarOpen ? 'ml-[220px]' : 'ml-[80px]'} bg-gradient-to-b from-white/80 to-muted/40 dark:from-zinc-900/90 dark:to-zinc-800/80`}>
        {/* Enhanced Header */}
        <header className="sticky top-0 z-30 flex items-center justify-center h-16 px-8 bg-background/90 border-b border-border/50 shadow-md overflow-visible">
          <div className="flex-1 flex items-center justify-center">
            <div className="font-extrabold text-3xl tracking-tight bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent drop-shadow-sm text-center">
              Proxima28 Admin Dashboard
            </div>
          </div>
          <div className="flex items-center gap-4 absolute right-8 z-40">
            {/* Enhanced Live Indicator */}
            <span className="inline-flex items-center gap-2 px-4 py-1.5 bg-green-100 dark:bg-green-900/40 rounded-full border border-green-300 dark:border-green-700 text-green-700 dark:text-green-400 font-semibold text-sm shadow-sm animate-pulse">
              <span className="h-2 w-2 rounded-full bg-green-500 animate-pulse shadow-green-500/50" />
              Live Data
            </span>
            <UserProfile />
          </div>
        </header>
        {/* Main Content */}
        <main className="flex-1 p-6 md:p-10 overflow-y-auto bg-gradient-to-b from-transparent to-muted/30">
          <div className="max-w-6xl mx-auto w-full">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
}; 