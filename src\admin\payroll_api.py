"""
FastAPI endpoints for payroll document processing and management.
Provides REST API for document upload, processing, and querying.
"""

import logging
import tempfile
import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, UploadFile, File, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from ..document_processing.payroll_processor import PayrollDocumentProcessor, ProcessingResult
from ..database.payroll_db import PayrollDatabase
from ..utils.logger import get_logger

logger = get_logger(__name__)

# Initialize router
router = APIRouter(prefix="/api/payroll", tags=["payroll"])

# Initialize processor (singleton)
payroll_processor = None

def get_payroll_processor() -> PayrollDocumentProcessor:
    """Get or create payroll processor instance."""
    global payroll_processor
    if payroll_processor is None:
        payroll_processor = PayrollDocumentProcessor()
    return payroll_processor


# Pydantic models for request/response
class DocumentUploadResponse(BaseModel):
    success: bool
    document_id: Optional[int] = None
    document_type: str
    processing_time: float
    confidence_score: float
    error_message: Optional[str] = None


class ComplaintAnalysisRequest(BaseModel):
    complaint_text: str = Field(..., description="Text of the HR complaint")
    employee_id: Optional[str] = Field(None, description="Optional employee ID for context")


class ComplaintAnalysisResponse(BaseModel):
    success: bool
    detected_issues: List[Dict[str, Any]]
    statistics: Dict[str, Any]
    processing_time: float
    error_message: Optional[str] = None


class DocumentSearchRequest(BaseModel):
    employee_id: Optional[str] = None
    organization: Optional[str] = None
    month: Optional[str] = None
    year: Optional[str] = None
    document_type: Optional[str] = None
    processing_status: Optional[str] = None
    limit: int = Field(50, ge=1, le=1000)


# API Endpoints

@router.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    employee_id: Optional[str] = Query(None, description="Employee ID for the document"),
    processor: PayrollDocumentProcessor = Depends(get_payroll_processor)
):
    """
    Upload and process a payroll document.
    
    Supports PDF, images, and other document formats.
    Returns extracted information and detected issues.
    """
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Validate file type
    allowed_extensions = {'.pdf', '.jpg', '.jpeg', '.png', '.docx', '.doc', '.txt'}
    file_extension = Path(file.filename).suffix.lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type: {file_extension}. Allowed: {', '.join(allowed_extensions)}"
        )
    
    # Save uploaded file temporarily
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            shutil.copyfileobj(file.file, temp_file)
            temp_path = Path(temp_file.name)
        
        # Process the document
        result = processor.process_document(temp_path, employee_id)
        
        # Clean up temporary file
        temp_path.unlink()
        
        return DocumentUploadResponse(
            success=result.success,
            document_id=result.document_id,
            document_type=result.document_type,
            processing_time=result.processing_time,
            confidence_score=result.confidence_score,
            error_message=result.error_message
        )
        
    except Exception as e:
        logger.error(f"Document upload failed: {e}")
        # Clean up temporary file if it exists
        if 'temp_path' in locals() and temp_path.exists():
            temp_path.unlink()
        
        raise HTTPException(status_code=500, detail=f"Document processing failed: {str(e)}")


@router.post("/upload-batch")
async def upload_batch_documents(
    files: List[UploadFile] = File(...),
    employee_ids: Optional[str] = Query(None, description="Comma-separated employee IDs (optional)"),
    processor: PayrollDocumentProcessor = Depends(get_payroll_processor)
):
    """
    Upload and process multiple payroll documents in batch.
    """
    if len(files) > 20:  # Limit batch size
        raise HTTPException(status_code=400, detail="Maximum 20 files allowed per batch")
    
    # Parse employee IDs if provided
    employee_id_list = None
    if employee_ids:
        employee_id_list = [id.strip() for id in employee_ids.split(',')]
        if len(employee_id_list) != len(files):
            raise HTTPException(
                status_code=400, 
                detail="Number of employee IDs must match number of files"
            )
    
    temp_paths = []
    results = []
    
    try:
        # Save all files temporarily
        for i, file in enumerate(files):
            if not file.filename:
                continue
                
            file_extension = Path(file.filename).suffix.lower()
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                shutil.copyfileobj(file.file, temp_file)
                temp_paths.append(Path(temp_file.name))
        
        # Process batch
        batch_results = processor.process_batch(
            temp_paths, 
            employee_id_list[:len(temp_paths)] if employee_id_list else None
        )
        
        # Convert to response format
        for result in batch_results:
            results.append({
                "success": result.success,
                "document_id": result.document_id,
                "document_type": result.document_type,
                "processing_time": result.processing_time,
                "confidence_score": result.confidence_score,
                "error_message": result.error_message
            })
        
        return {
            "success": True,
            "processed_count": len(results),
            "successful_count": sum(1 for r in results if r["success"]),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Batch upload failed: {e}")
        raise HTTPException(status_code=500, detail=f"Batch processing failed: {str(e)}")
    
    finally:
        # Clean up temporary files
        for temp_path in temp_paths:
            if temp_path.exists():
                temp_path.unlink()


@router.post("/analyze-complaint", response_model=ComplaintAnalysisResponse)
async def analyze_complaint(
    request: ComplaintAnalysisRequest,
    processor: PayrollDocumentProcessor = Depends(get_payroll_processor)
):
    """
    Analyze an HR complaint and detect potential issues.
    """
    try:
        result = processor.analyze_complaint(request.complaint_text, request.employee_id)
        
        return ComplaintAnalysisResponse(
            success=result["success"],
            detected_issues=result.get("detected_issues", []),
            statistics=result.get("statistics", {}),
            processing_time=result["processing_time"],
            error_message=result.get("error_message")
        )
        
    except Exception as e:
        logger.error(f"Complaint analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.get("/documents/{document_id}")
async def get_document_details(
    document_id: int,
    processor: PayrollDocumentProcessor = Depends(get_payroll_processor)
):
    """
    Get detailed information about a specific document.
    """
    try:
        details = processor.get_document_details(document_id)
        
        if not details:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return details
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get document details: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve document: {str(e)}")


@router.post("/documents/search")
async def search_documents(
    request: DocumentSearchRequest,
    processor: PayrollDocumentProcessor = Depends(get_payroll_processor)
):
    """
    Search documents with flexible criteria.
    """
    try:
        db = processor.db
        
        # Build search query
        query = {}
        if request.employee_id:
            query["employee_id"] = request.employee_id
        if request.organization:
            query["organization"] = request.organization
        if request.month:
            query["month"] = request.month
        if request.year:
            query["year"] = request.year
        if request.document_type:
            query["document_type"] = request.document_type
        if request.processing_status:
            query["processing_status"] = request.processing_status
        
        # Search documents
        documents = db.search_documents(query, request.limit)
        
        # Convert to dict format
        results = []
        for doc in documents:
            doc_dict = {
                "id": doc.id,
                "employee_id": doc.employee_id,
                "employee_name": doc.employee_name,
                "organization": doc.organization,
                "month": doc.month,
                "year": doc.year,
                "document_type": doc.document_type,
                "processing_status": doc.processing_status,
                "confidence_score": doc.confidence_score,
                "created_at": doc.created_at
            }
            results.append(doc_dict)
        
        return {
            "success": True,
            "count": len(results),
            "documents": results
        }
        
    except Exception as e:
        logger.error(f"Document search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/employee/{employee_id}/documents")
async def get_employee_documents(
    employee_id: str,
    limit: int = Query(50, ge=1, le=200),
    processor: PayrollDocumentProcessor = Depends(get_payroll_processor)
):
    """
    Get all documents for a specific employee.
    """
    try:
        db = processor.db
        documents = db.get_documents_by_employee(employee_id, limit)
        
        results = []
        for doc in documents:
            doc_dict = {
                "id": doc.id,
                "employee_name": doc.employee_name,
                "organization": doc.organization,
                "month": doc.month,
                "year": doc.year,
                "document_type": doc.document_type,
                "processing_status": doc.processing_status,
                "confidence_score": doc.confidence_score,
                "created_at": doc.created_at
            }
            results.append(doc_dict)
        
        return {
            "success": True,
            "employee_id": employee_id,
            "count": len(results),
            "documents": results
        }
        
    except Exception as e:
        logger.error(f"Failed to get employee documents: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve documents: {str(e)}")


@router.get("/statistics")
async def get_processing_statistics(
    processor: PayrollDocumentProcessor = Depends(get_payroll_processor)
):
    """
    Get overall processing statistics for the dashboard.
    """
    try:
        stats = processor.get_processing_statistics()
        return {
            "success": True,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve statistics: {str(e)}")


@router.put("/issues/{issue_id}/resolve")
async def resolve_issue(
    issue_id: int,
    resolved_by: str = Query(..., description="User who resolved the issue"),
    processor: PayrollDocumentProcessor = Depends(get_payroll_processor)
):
    """
    Mark an issue as resolved.
    """
    try:
        db = processor.db
        db.resolve_issue(issue_id, resolved_by)
        
        return {
            "success": True,
            "message": f"Issue {issue_id} resolved by {resolved_by}"
        }
        
    except Exception as e:
        logger.error(f"Failed to resolve issue: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to resolve issue: {str(e)}")


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "payroll-processor"
    }
