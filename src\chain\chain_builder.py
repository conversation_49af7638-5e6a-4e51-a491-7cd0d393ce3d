import os
import re
import time
import asyncio
import traceback
from typing import List, Dict, Any
from langchain_groq import <PERSON>tG<PERSON><PERSON>
from langchain.schema import HumanMessage, AIMessage

from ..utils.logger import get_logger
from ..retrieval.context_builder import Context<PERSON>uilder
from ..conversation.history_manager import HistoryManager
from ..conversation.language_detector import detect_language
from .prompt_templates import (
    create_hr_assistant_prompt,
    create_clarification_prompt
)
from ..config import (
    GROQ_API_KEY, LLM_MODEL_NAME, HR_EMAILS, ENABLE_EMAIL_ESCALATION,
    INTENT_CLASSIFIER_CONFIDENCE_THRESHOLD, MAX_TOKENS_PER_MODE, UNIFIED_MODEL_NAME,
    DEBUG, ENABLE_CONTEXT_DEBUG, EMBEDDING_MODEL_DIR, EMBEDDING_TOKENIZER_DIR
)
from ..utils.api_status import API<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ..utils.email_service import EmailService
from ..intent.intent_classifier import IntentClassifier # Ensure this import is present
from ..ner.entity_extractor import EntityExtractor     # Ensure this import is present

from ..document_processing.version_control import DocumentVersionControl
from transformers import AutoTokenizer
import requests
from ..retrieval.context_filter import trim_context_to_token_limit

logger = get_logger(__name__)

GREETING_KEYWORDS = ["hi", "hello", "hey", "good morning", "good afternoon", "good evening", "howdy"]

def is_pure_greeting(text: str) -> bool:
    return any(re.fullmatch(rf"(?i){kw}[!. ]*", text.strip()) for kw in GREETING_KEYWORDS)

def sanitize_input(text: str) -> str:
    return re.sub(r"[\n\r\t]+", " ", text.strip())

def contains_sensitive_info(text: str) -> bool:
    """
    Enhanced sensitive information detection with better HR query handling.
    Avoids false positives for common HR queries.
    """
    # Allow common HR terms that might contain sensitive keywords
    hr_safe_patterns = [
        r"policy", r"leave", r"benefits", r"offer letter", r"payslip",
        r"download", r"summarize", r"extract", r"document"
    ]

    # If query contains HR-safe patterns, be less strict
    if any(re.search(pattern, text, re.IGNORECASE) for pattern in hr_safe_patterns):
        # Only flag if it contains actual sensitive data patterns (not just keywords)
        sensitive_patterns = [
            r"\b\d{4}\s?\d{4}\s?\d{4}\b",  # Aadhaar-like numbers
            r"\b[A-Z]{5}\d{4}[A-Z]\b",     # PAN-like format
            r"\b\d{4}-\d{4}-\d{4}-\d{4}\b", # Credit card-like
            r"password\s*[:=]\s*\S+",       # Password assignments
        ]
        result = any(re.search(pattern, text, re.IGNORECASE) for pattern in sensitive_patterns)
        if result:
            logger.warning(f"Sensitive data pattern detected: {[p for p in sensitive_patterns if re.search(p, text, re.IGNORECASE)]}")
        return result

    # Original strict detection for non-HR queries
    result = bool(re.search(r"(pan|aadhaar|salary|bank account|ifsc|dob|ssn|passport)", text, re.IGNORECASE))
    if result:
        logger.warning(f"Sensitive keyword detected in non-HR query: {text[:50]}...")
    return result

MAX_TOTAL_TOKENS = 4000
RESERVED_FOR_OUTPUT = 750  # model response
RESERVED_FOR_QUERY = 100   # user query
RESERVED_FOR_TEMPLATE = 200  # prompt formatting
MAX_CONTEXT_TOKENS = MAX_TOTAL_TOKENS - RESERVED_FOR_OUTPUT - RESERVED_FOR_QUERY - RESERVED_FOR_TEMPLATE
MIN_CONTEXT_TOKENS = 500

# Token budgeting constants
MAX_TOKENS = 6000
RESERVED_FOR_OUTPUT = 1000
MAX_PROMPT_TOKENS = MAX_TOKENS - RESERVED_FOR_OUTPUT
CONTEXT_BUDGET_RATIO = 0.75  # 75% of prompt budget for context, rest for history/system/query
MAX_HISTORY_TURNS = 2

class ChainBuilder:
    def __init__(self, model_name=LLM_MODEL_NAME, api_key=GROQ_API_KEY,
                 context_builder=None, history_manager=None, email_service=None):

        if not api_key:
            raise ValueError("GROQ_API_KEY is required but not set")
        if not model_name:
            raise ValueError("LLM_MODEL_NAME is required but not set")

        # Initialize tokenizer for context builder
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(UNIFIED_MODEL_NAME)
            logger.info(f"Loaded tokenizer from {UNIFIED_MODEL_NAME}")
        except Exception as e:
            logger.warning(f"Failed to load tokenizer: {e}. Context builder will use fallback.")
            self.tokenizer = None

        logger.info(f"Initializing ChainBuilder with model: {model_name}")

        self.model_name = model_name
        self.api_key = api_key
        self.api_status_checker = APIStatusChecker()
        # Load the tokenizer from the local embedding model path
        try:
            tokenizer = AutoTokenizer.from_pretrained('data/models_cache/bge-base-en-v1.5')
            logger.info("Loaded tokenizer from bge-base-en-v1.5")
        except Exception as e:
            logger.warning(f"Failed to load tokenizer: {e}. Using fallback.")
            # Use a simple word-based tokenizer as fallback
            def simple_tokenizer(text):
                return len(text.split())
            tokenizer = simple_tokenizer
        
        token_count_fn = lambda text: len(tokenizer.encode(text))
        self.context_builder = context_builder or ContextBuilder(tokenizer=token_count_fn, min_content_length=30)
        self.history_manager = history_manager or HistoryManager()
        self.email_service = email_service or EmailService()
        self.intent_classifier = IntentClassifier() # Initialized here
        self.entity_extractor = EntityExtractor()     # Initialized here

        self.version_control = DocumentVersionControl()
        self.hr_emails = HR_EMAILS
        self.enable_email_escalation = ENABLE_EMAIL_ESCALATION

        self.llm = ChatGroq(
            model=model_name,
            groq_api_key=api_key,
            max_tokens=750,
            temperature=0.1,
            timeout=120,  # Increased timeout to 120 seconds
            model_kwargs={
                "top_p": 0.9,
                "frequency_penalty": 0.2,
                "presence_penalty": 0.6,
            },
        )

    def format_response(self, text: str) -> str:
        if not text.strip():
            return "I'm sorry, I couldn't find a helpful response."

        paragraphs = text.split("\n\n")
        formatted = []

        for para in paragraphs:
            lines = para.splitlines()
            if any(para.lower().startswith(k.lower()) for k in ["What is", "Causes of", "Symptoms of", "Types of", "Prevention of"]):
                formatted.append(f"### {para}")
            elif all(line.strip().startswith(("-", "*")) for line in lines if line.strip()):
                formatted.extend([line.strip() for line in lines])
            elif all(line.strip()[:2].isdigit() and line.strip()[2] == '.' for line in lines if line.strip()):
                formatted.extend(lines)
            else:
                formatted.append(para)

        return "\n\n".join(formatted)

    def format_bullets(self, text: str) -> str:
        import re
        lines = text.split('\n')
        formatted = []
        in_list = False
        for idx, line in enumerate(lines):
            line = line.strip()
            if not line:
                if in_list:
                    in_list = False
                formatted.append("")
                continue  # preserve blank lines for markdown
            # Heading: line is all bold (starts and ends with **, and not a bullet)
            if re.match(r'^\*\*.+\*\*$', line) and not re.match(r'^(\* |- |• )', line):
                if in_list:
                    in_list = False
                # Use markdown heading for the first heading, bold for others
                if idx == 0:
                    formatted.append(f'### {line.strip("*")}')
                else:
                    formatted.append(f'**{line.strip("*")}**')
                continue
            # Real bullet: starts with '* ', '- ', or '• '
            if re.match(r'^(\* |- |• )', line):
                content = re.sub(r'^(\* |- |• )', '', line).strip()
                if not in_list:
                    in_list = True
                formatted.append(f'- {content}')
                continue
            # Not a bullet or heading
            if in_list:
                in_list = False
            formatted.append(line)
        # No HTML tags, just join with newlines
        return '\n'.join(formatted)

    async def run_chain(self, query: str, device_id: str, files_info: List[Dict[str, Any]] = None, retry_count: int = 0, response_mode: str = "detailed", email: str = None, employee_id: str = None, month: str = None, year: str = None, intent_result: Dict[str, Any] = None) -> Dict[str, Any]:
        logger.debug(f"[run_chain] Started for query: '{query}', device_id: {device_id}, retry_count: {retry_count}")
        
        # Always initialize intent and confidence
        intent = "unknown"
        confidence = 0.0
        entities = []
        # Use the provided response_mode parameter instead of hardcoded "auto"
        include_reasoning = True
        sources = []
        elapsed = 0.0
        needs_escalation = False
        language = "en"
        formatted_response = "I'm sorry, I couldn't process your request."
        
        try:
            query = sanitize_input(query)
            logger.debug(f"[run_chain] Sanitized query: '{query}'")

            if not self.api_status_checker.is_groq_operational():
                logger.warning("[run_chain] Groq API is not operational.")
                return {
                    "content": "The language model service is currently unavailable. Please try again later.",
                    "language": "en",
                    "sources": [],
                    "error": {"type": "ServiceUnavailable"}
                }
            logger.debug("[run_chain] Groq API is operational.")

            language = detect_language(query)
            logger.debug(f"[run_chain] Detected language: {language}")

            # Use passed intent_result if available, otherwise classify
            if intent_result:
                logger.debug("[run_chain] Using pre-classified intent result")
                intent = intent_result.get("intent", "unknown")
                confidence = intent_result.get("confidence", 0.0)
                logger.debug(f"[run_chain] Pre-classified intent: {intent}, confidence: {confidence}")
            else:
                try:
                    # Use the intent classifier
                    logger.debug("[run_chain] Starting intent classification...")
                    intent_result = self.intent_classifier.classify_intent(query)
                    intent = intent_result.get("intent", "unknown")
                    confidence = intent_result.get("confidence", 0.0)
                    logger.debug(f"[run_chain] Intent classified: {intent}, confidence: {confidence}")
                except Exception as e:
                    logger.warning(f"[run_chain] Intent classification failed: {e}")
                    intent, confidence = "unknown", 0.0

            if is_pure_greeting(query):
                logger.debug("[run_chain] Pure greeting detected, returning canned response.")
                return {
                    "content": "Hello! How can I assist you with HR-related matters today?",
                    "language": language,
                    "sources": [],
                    "intent": "greeting",
                    "intent_confidence": 1.0,
                    "entities": [],
                    "response_mode": response_mode,
                    "include_reasoning": include_reasoning
                }

            if contains_sensitive_info(query):
                logger.warning("[run_chain] Sensitive information detected in user input.")

            try:
                # Use the entity extractor
                entities = self.entity_extractor.extract_entities(query)
                logger.debug(f"[run_chain] Extracted entities: {entities}")
            except Exception as e:
                logger.warning(f"[run_chain] Entity extraction failed: {e}")
                entities = []

            # --- Payslip API integration ---
            if intent == "payslip_request":
                # Use month and year from arguments if provided, else try to extract from NER
                if not month or not year:
                    for ent in (entities[0]["entities"] if entities and isinstance(entities, list) and "entities" in entities[0] else []):
                        if ent["label"] == "MONTH" and not month:
                            month = ent["text"]
                        elif ent["label"] == "YEAR" and not year:
                            year = ent["text"]
                # Do NOT use NER for email or employee_id, only use arguments
                if not email or not employee_id:
                    return {
                        "content": "Your profile is missing email or employee ID. Please update your personal information in the settings panel.",
                        "language": language,
                        "sources": [],
                        "intent": intent,
                        "intent_confidence": confidence,
                        "entities": entities,
                        "response_mode": response_mode,
                        "include_reasoning": include_reasoning
                    }
                if month and year and email and employee_id:
                    try:
                        payslip_api_url = os.getenv("SALARY_SLIP_URL")
                        params = {
                            "employee_id": employee_id,
                            "email": email,
                            "month": month,
                            "year": year
                        }
                        resp = requests.get(payslip_api_url, params=params, timeout=10)
                        if resp.status_code == 200:
                            payslips = resp.json()
                            download_url = None
                            for slip in payslips:
                                if (
                                    str(slip.get("employee_id")) == str(employee_id)
                                    and slip.get("email", "").lower() == email.lower()
                                    and str(slip.get("year")) == str(year)
                                    and (
                                        str(slip.get("month", "")).lower() == str(month).lower()
                                        or str(slip.get("month_name", "")).lower() == str(month).lower()
                                    )
                                ):
                                    download_url = slip.get("download_url")
                                    break
                            if download_url:
                                return {
                                    "content": f"Your payslip for {month} {year} is ready. [Download here]({download_url})",
                                    "language": language,
                                    "sources": [],
                                    "intent": intent,
                                    "intent_confidence": confidence,
                                    "entities": entities,
                                    "response_mode": response_mode,
                                    "include_reasoning": include_reasoning
                                }
                            else:
                                logger.warning(f"No matching payslip found in payslip API response: {payslips}")
                        else:
                            logger.warning(f"Payslip API returned {resp.status_code}: {resp.text}")
                    except Exception as e:
                        logger.error(f"Payslip API call failed: {e}")
                # If missing info or API fails, fallback to default
                return {
                    "content": "Sorry, I couldn't retrieve your payslip. Please check your details or contact HR.",
                    "language": language,
                    "sources": [],
                    "intent": intent,
                    "intent_confidence": confidence,
                    "entities": entities,
                    "response_mode": response_mode,
                    "include_reasoning": include_reasoning
                }
            # --- End Payslip API integration ---

            if files_info:
                logger.debug(f"[run_chain] Processing files: {files_info}")
                for file in files_info:
                    path = file.get("path")
                    if path and self.version_control.check_version(path):
                        self.version_control.reindex_document(path)
                        logger.debug(f"[run_chain] Reindexed document: {path}")

            try:
                history_items = self.history_manager.get_history(device_id)[-5:]
                history_msgs = [
                    item for h in history_items
                    for item in [HumanMessage(content=f"User: {h['user_query']}"), AIMessage(content=h['assistant_response'])]
                ]
                logger.debug(f"[run_chain] Retrieved history items: {len(history_items)}")
            except Exception as e:
                logger.warning(f"[run_chain] History retrieval failed: {e}")
                history_msgs = []

            try:
                # Determine max_tokens based on response_mode
                mode = response_mode if response_mode in MAX_TOKENS_PER_MODE else "detailed"
                max_output_tokens = MAX_TOKENS_PER_MODE[mode]
                MAX_MODEL_TOKENS = 4000  # Reduced from 4096/6000 to 4000
                PROMPT_TEMPLATE_TOKENS = 200
                USER_QUERY_TOKENS = 100
                available_context_tokens = (
                    MAX_MODEL_TOKENS - PROMPT_TEMPLATE_TOKENS - USER_QUERY_TOKENS - max_output_tokens
                )

                logger.debug(f"[run_chain] Building context with max_tokens={available_context_tokens}")
                context_result = await self.context_builder.build_context(query, max_tokens=available_context_tokens, files_info=files_info)

                if context_result is not None:
                    context = context_result.context
                    sources = context_result.sources

                    # Log context building results
                    logger.info(f"[run_chain] Context built: {len(context)} chars, {context_result.documents_found} docs found, {context_result.documents_used} docs used")
                    logger.debug(f"[run_chain] Context preview: {context[:200]}...")

                    # Enforce token limit on context
                    context_chunks = context.split("\n\n---\n\n")
                    context_chunks = trim_context_to_token_limit(context_chunks, available_context_tokens, self.context_builder.tokenizer)
                    context = "\n\n---\n\n".join(context_chunks)
                    total_context_tokens = sum(self.context_builder.tokenizer(chunk) for chunk in context_chunks)

                    # Additional debugging for empty context
                    if not context.strip():
                        logger.warning(f"[run_chain] Context is empty after building! Documents found: {context_result.documents_found}, used: {context_result.documents_used}")
                else:
                    context, sources, total_context_tokens = "", [], 0
                    logger.warning("[run_chain] Context result is None!")

                logger.info({
                    "mode": response_mode,
                    "context_tokens": total_context_tokens,
                    "context_length": len(context),
                    "output_tokens": max_output_tokens,
                    "query_tokens": USER_QUERY_TOKENS,
                    "template_tokens": PROMPT_TEMPLATE_TOKENS,
                    "total_payload": total_context_tokens + max_output_tokens + USER_QUERY_TOKENS + PROMPT_TEMPLATE_TOKENS,
                    "sources_count": len(sources)
                })
                logger.debug(f"[run_chain] Context built. Sources: {len(sources)} sources")
            except Exception as e:
                logger.error(f"[run_chain] Context building failed: {e}", exc_info=True)
                context, sources, total_context_tokens = "", [], 0

            try:
                # Advanced prompt engineering: use provided response mode and detect reasoning needs
                from .prompt_templates import should_include_reasoning
                include_reasoning = should_include_reasoning(query, intent, confidence)
                logger.debug(f"[run_chain] Using response_mode: {response_mode}, include_reasoning: {include_reasoning}")

                # 1. Prepare system message (keep concise)
                system_message = (
                    "You are a helpful HR assistant. Only answer using the given context.\n"
                    "If the answer is not in the context, say so clearly.\n"
                )
                system_tokens = self.context_builder.tokenizer(system_message)

                # 2. Prepare user query
                query_tokens = self.context_builder.tokenizer(query)

                # 3. Prepare context chunks (prioritize by relevance)
                context_chunks = context.split("\n\n---\n\n") if context else []
                trimmed_context = []
                context_tokens = 0
                context_budget = int(MAX_PROMPT_TOKENS * CONTEXT_BUDGET_RATIO)
                for chunk in context_chunks:
                    chunk_tokens = self.context_builder.tokenizer(chunk)
                    if context_tokens + chunk_tokens > context_budget:
                        break
                    trimmed_context.append(chunk)
                    context_tokens += chunk_tokens
                final_context = "\n\n---\n\n".join(trimmed_context)

                # 4. Prepare conversation history (most recent N turns, trim if needed)
                history_msgs = history_msgs[-MAX_HISTORY_TURNS:] if history_msgs else []
                trimmed_history = []
                history_tokens = 0
                for msg in history_msgs:
                    msg_str = str(msg)
                    msg_tokens = self.context_builder.tokenizer(msg_str)
                    if system_tokens + context_tokens + history_tokens + query_tokens + msg_tokens > MAX_PROMPT_TOKENS:
                        break
                    trimmed_history.append(msg)
                    history_tokens += msg_tokens

                # 5. Final fail-safe: trim context/history if still over budget
                while (system_tokens + context_tokens + history_tokens + query_tokens) > MAX_PROMPT_TOKENS and trimmed_context:
                    # Remove least relevant context chunk
                    removed = trimmed_context.pop()
                    context_tokens -= self.context_builder.tokenizer(removed)
                    final_context = "\n\n---\n\n".join(trimmed_context)
                while (system_tokens + context_tokens + history_tokens + query_tokens) > MAX_PROMPT_TOKENS and trimmed_history:
                    # Remove oldest history
                    removed = trimmed_history.pop(0)
                    history_tokens -= self.context_builder.tokenizer(str(removed))

                # After context is built and before prompt is rendered:
                logger.info(f"[run_chain] Retrieved context: {context[:500]}..." if len(context) > 500 else f"[run_chain] Retrieved context: {context}")
                logger.info(f"[run_chain] Sources used: {sources}")

                # Assemble prompt using trimmed parts
                prompt_template = create_hr_assistant_prompt()
                inputs = {
                    "query": query,
                    "context": final_context,
                    "history": trimmed_history,
                    "response_mode": response_mode,
                }
                prompt_obj = prompt_template.format_prompt(**inputs)
                full_prompt_str = str(prompt_obj)
                logger.info(f"[run_chain] Rendered prompt string (first 1000 chars): {full_prompt_str[:1000]}")

                # Set prompt_used for output
                prompt_used = full_prompt_str

                # 6. Assemble prompt using trimmed parts
                prompt_template = create_hr_assistant_prompt()
                inputs = {
                    "query": query,
                    "context": final_context,
                    "history": trimmed_history,
                    "response_mode": response_mode,
                }
                prompt_obj = prompt_template.format_prompt(**inputs)
                full_prompt_str = str(prompt_obj)
                total_prompt_tokens = self.context_builder.tokenizer(full_prompt_str)
                if total_prompt_tokens > MAX_PROMPT_TOKENS:
                    logger.error(f"[run_chain] Unable to trim prompt below LLM limit. Aborting request.")
                    return await self._error("TokenLimitExceeded", Exception(f"Prompt tokens {total_prompt_tokens} exceeds limit {MAX_PROMPT_TOKENS}"), retry_count, intent, confidence, language, sources, entities, response_mode, include_reasoning)

                # Log the formatted prompt for debugging
                logger.debug(f"[run_chain] Formatted prompt length: {len(full_prompt_str)}")

                # Enhanced debug logging when enabled
                if DEBUG or ENABLE_CONTEXT_DEBUG:
                    logger.info(f"[DEBUG] Full prompt content: {full_prompt_str}")
                    logger.info(f"[DEBUG] Context used: {context[:500]}..." if len(context) > 500 else f"[DEBUG] Context used: {context}")
                else:
                    logger.debug(f"[run_chain] Prompt preview: {full_prompt_str[:300]}...")

            except Exception as e:
                logger.error(f"[run_chain] Prompt creation failed: {e}", exc_info=True)
                return await self._error("PromptCreationError", e, retry_count, intent, confidence, language, sources, entities, response_mode, include_reasoning)

            try:
                start = time.time()
                logger.info(f"[run_chain] About to invoke LLM with prompt length: {len(full_prompt_str)}")
                
                # Double-check Groq API status before making the call
                if not self.api_status_checker.is_groq_operational():
                    logger.error("[run_chain] Groq API is not operational before LLM call")
                    return await self._error("ServiceUnavailable", Exception("Groq API is not operational"), retry_count, intent, confidence, language, sources, entities, response_mode, include_reasoning)
                
                # Add timeout to the LLM call
                response = await asyncio.wait_for(
                    asyncio.to_thread(self.llm.invoke, prompt_obj),
                    timeout=30.0  # Reduced timeout to 30 seconds
                )
                elapsed = time.time() - start
                raw_response = getattr(response, "content", str(response)).strip()
                logger.info(f"[run_chain] LLM invoked successfully in {elapsed:.2f}s. Raw response length: {len(raw_response)}")
            except asyncio.TimeoutError:
                logger.error("[run_chain] LLM invocation timed out after 30 seconds")
                return await self._error("TimeoutError", Exception("LLM request timed out after 30 seconds"), retry_count, intent, confidence, language, sources, entities, response_mode, include_reasoning)
            except Exception as e:
                logger.error(f"[run_chain] LLM invocation failed: {e}")
                # Log warning if rate limit (HTTP 429) or context length (HTTP 413) is detected
                status_code = getattr(e, 'status_code', None)
                if status_code in [429, 413] or 'context length' in str(e).lower() or 'too many tokens' in str(e).lower():
                    logger.warning(f"[run_chain] LLM API error {status_code} (likely token/context issue). Retrying with reduced context.")
                    if retry_count < 5:
                        await asyncio.sleep(2 ** retry_count)
                        return await self.run_chain(query, device_id, files_info, retry_count + 1, response_mode, email, employee_id, month, year)
                elif retry_count < 5:
                    logger.warning(f"[run_chain] Retrying LLM invocation ({retry_count + 1}/5).")
                    await asyncio.sleep(2 ** retry_count)
                    return await self.run_chain(query, device_id, files_info, retry_count + 1, response_mode, email, employee_id, month, year)
                return await self._error(type(e).__name__, e, retry_count, intent, confidence, language, sources, entities, response_mode, include_reasoning)

            needs_escalation = "[ESCALATE_TO_HR]" in raw_response
            if needs_escalation:
                logger.debug("[run_chain] Escalation tag detected.")
                raw_response = raw_response.replace("[ESCALATE_TO_HR]", "").strip()
                # Don't add extra text if email escalation is disabled
                if self.enable_email_escalation and self.hr_emails:
                    raw_response += "\n\n**Would you like me to escalate this to HR?**"

            try:
                formatted_response = self.format_response(raw_response)
                logger.debug(f"[run_chain] Response formatted. Length: {len(formatted_response)}")
                

            except Exception as e:
                logger.warning(f"[run_chain] Response formatting failed: {e}")
                formatted_response = raw_response

            # Apply bullet formatting
            formatted_response = self.format_bullets(formatted_response)

            # Apply response mode post-processing
            if response_mode == "concise":
                original_length = len(formatted_response)
                formatted_response = self.make_response_concise(formatted_response)
                logger.debug(f"[run_chain] Applied concise mode: {original_length} -> {len(formatted_response)} characters")

            # Prepare final result with minimal processing
            final_result = {
                "content": formatted_response,
                "language": language,
                "sources": sources,
                "response_time": elapsed,
                "escalated": needs_escalation,
                "auto_resolved": False,  # Will be set by payroll processor if applicable
                "intent": intent,
                "intent_confidence": confidence,
                "entities": entities,
                "response_mode": response_mode,
                "include_reasoning": include_reasoning,
                # Additional fields expected by app.py
                "prompt": prompt_used,
                "raw_output": formatted_response,
                "retrieved_doc_count": len(sources),
                "final_summary": formatted_response
            }
            
            # Log escalation event for monitoring
            if needs_escalation:
                logger.info(f"[run_chain] Escalation triggered for query: {query[:100]}...")
            
            logger.debug(f"[run_chain] Final result prepared: {final_result.keys()}")
            logger.debug("[run_chain] Returning final result.")
            return final_result

        except Exception as e:
            logger.error(f"[run_chain] Unhandled exception in run_chain: {e}")
            logger.error(f"AttributeError: {e}")
            return await self._error(type(e).__name__, e, retry_count, intent, confidence, language, sources, entities, response_mode, include_reasoning)

    async def _error(self, error_type: str, error: Exception, retry: int = 0, intent: str = "unknown", confidence: float = 0.0, language: str = "en", sources=None, entities=None, response_mode: str = "auto", include_reasoning: bool = True) -> Dict[str, Any]:
        logger.error(f"{error_type}: {error}")
        trace = traceback.format_exc()
        if sources is None:
            sources = []
        if entities is None:
            entities = []
        message_map = {
            "timeout": "The request timed out. Please try again.",
            "api key": "Invalid API key provided. Please check your configuration.",
            "rate limit": "Rate limit exceeded. Please try again shortly.",
            "context length": "The input is too long. Please shorten your query."
        }
        friendly_msg = "I'm sorry, something went wrong. Please try again."
        for keyword, msg in message_map.items():
            if keyword in str(error).lower():
                friendly_msg = msg
                break
        return {
            "content": friendly_msg,
            "language": language,
            "sources": sources,
            "error": {
                "type": error_type,
                "message": str(error),
                "traceback": trace,
                "retry_attempted": retry > 0
            },
            "intent": intent,
            "intent_confidence": confidence,
            "entities": entities,
            "response_mode": response_mode,
            "include_reasoning": include_reasoning,
            # Additional fields expected by app.py
            "prompt": None,
            "raw_output": friendly_msg,
            "retrieved_doc_count": len(sources) if sources else 0,
            "final_summary": friendly_msg
        }

    def get_vector_database_count(self) -> int:
        """Get the number of vectors in the vector database."""
        try:
            # Fix for Qdrant vector store - use proper method to get count
            if hasattr(self.context_builder, 'vector_search') and hasattr(self.context_builder.vector_search, 'vector_store'):
                vector_store = self.context_builder.vector_search.vector_store
                
                # For Qdrant, use the client to get collection info
                if hasattr(vector_store, 'client') and hasattr(vector_store, 'collection_name'):
                    try:
                        collection_info = vector_store.client.get_collection(vector_store.collection_name)
                        return collection_info.points_count if collection_info else 0
                    except Exception as e:
                        logger.debug(f"Could not get collection info from Qdrant: {e}")
                        return 0
                
                # Fallback for other vector stores with index attribute
                elif hasattr(vector_store, 'index') and vector_store.index:
                    return getattr(vector_store.index, 'ntotal', 0)
                
                # Another fallback - try to get count via similarity search
                try:
                    # Try a dummy search to see if there are any documents
                    results = vector_store.similarity_search("test", k=1)
                    return len(results) if results else 0
                except:
                    return 0
                
            return 0
        except Exception as e:
            logger.error(f"Error getting vector database count: {e}")
            return 0

    def make_response_concise(self, response: str) -> str:
        """Make a response more concise by extracting key information."""
        try:
            # If response is already short (less than 200 characters), return as is
            if len(response) <= 200:
                return response
                
            # Extract bullet points or key facts
            lines = response.split('\n')
            concise_parts = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # Keep important bullet points and key information
                if (line.startswith(('•', '-', '*', '1.', '2.', '3.', '4.', '5.')) and 
                    len(line) < 120 and 
                    not line.lower().startswith(('need help', 'questions', 'feel free', 'contact'))):
                    concise_parts.append(line)
                elif (line.startswith('**') and line.endswith('**') and 
                      len(line) < 80):  # Bold headers
                    concise_parts.append(line)
                elif (len(line.split()) <= 25 and 
                      any(word in line.lower() for word in ['policy', 'days', 'hours', 'required', 'allowed', 'prohibited', 'attire', 'dress', 'friday', 'men', 'women', 'leave', 'balance', 'entitlement', 'eligible'])):
                    concise_parts.append(line)
            
            # If we found key points, return them (limit to 3-4 points)
            if len(concise_parts) >= 2:
                return '\n'.join(concise_parts[:4])
            
            # If no good bullet points found, extract first meaningful sentence
            sentences = response.split('.')
            for sentence in sentences:
                sentence = sentence.strip()
                if (len(sentence) > 20 and 
                    len(sentence) < 150 and 
                    not sentence.lower().startswith(('need help', 'questions', 'feel free', 'contact', 'i can help'))):
                    return sentence + '.'
            
            # Fallback: return first 150 characters
            return response[:150] + ('...' if len(response) > 150 else '')
            
        except Exception as e:
            logger.warning(f"Error making response concise: {e}")
            return response

    async def summarize(self, document: str, source_file: str = None) -> str:
        """Generate a concise, actionable summary for the given document using the LLM. Fallback to extractive summary if LLM fails."""
        try:
            if not document or len(document.strip()) < 20:
                return "Document is too short to summarize."

            # Enhanced prompt with source file reference
            source_ref = f" from {source_file}" if source_file else ""
            prompt = (
                f"You are an expert HR assistant. Summarize the following document{source_ref} in 3-5 concise bullet points. "
                "Focus on the most important policies, actions, or facts. Avoid generic statements. "
                "Limit your summary to 150 words. If this is an offer letter, include key details like position, salary range (if mentioned), start date, and benefits.\n\n"
                f"Document:\n{document}\n\nSummary (bullet points):"
            )

            logger.debug(f"[summarize] Generating summary for document of {len(document)} chars{source_ref}")
            response = await self.llm.invoke([HumanMessage(content=prompt)])
            summary = response.content.strip() if hasattr(response, 'content') else str(response).strip()

            # Fallback if summary is empty or too generic
            if not summary or len(summary.split()) < 5:
                # Extract first 3-5 sentences as fallback
                import re
                sentences = re.split(r'(?<=[.!?]) +', document)
                fallback = '\n'.join(sentences[:5])
                logger.warning(f"[summarize] LLM summary too short, using fallback for {source_file or 'unknown file'}")
                return f"[LLM unavailable, fallback summary{source_ref}:]\n{fallback}"

            logger.info(f"[summarize] Successfully generated summary of {len(summary)} chars{source_ref}")
            return summary
        except Exception as e:
            # Fallback: extract first 3-5 sentences
            import re
            sentences = re.split(r'(?<=[.!?]) +', document)
            fallback = '\n'.join(sentences[:5])
            logger.error(f"[summarize] Error generating summary{source_ref}: {e}")
            return f"[Error generating summary: {e}]\n{fallback}"

    async def run_clarification(
        self,
        selected_text: str,
        previous_message: str,
        clarification_query: str,
        device_id: str,
        language: str = "en",
        response_mode: str = "concise"
    ) -> Dict[str, Any]:
        """
        Generate a clarification response for a selected portion of a previous message.

        Args:
            selected_text (str): The text the user selected for clarification
            previous_message (str): The full previous assistant message
            clarification_query (str): The user's clarification request (optional)
            device_id (str): Device/session identifier for context
            language (str): Response language
            response_mode (str): Response mode (concise/detailed)

        Returns:
            Dict[str, Any]: Clarification response with metadata
        """
        logger.debug(f"[run_clarification] Started for selected_text: '{selected_text[:50]}...'")

        try:
            # Get recent conversation history for context
            history = self.history_manager.get_history(device_id)

            # Build minimal context from recent history if needed
            context = ""
            if history:
                # Get last few exchanges for context
                recent_history = history[-3:] if len(history) > 3 else history
                context_parts = []
                for entry in recent_history:
                    if entry.get("user_query"):
                        context_parts.append(f"User: {entry['user_query']}")
                    if entry.get("assistant_response"):
                        context_parts.append(f"Assistant: {entry['assistant_response'][:200]}...")
                context = "\n".join(context_parts)

            # Create clarification prompt
            prompt_template = create_clarification_prompt(language=language, response_mode=response_mode)

            # Format the prompt
            formatted_prompt = prompt_template.format(
                previous_message=previous_message,
                selected_text=selected_text,
                clarification_query=clarification_query or "Please explain this part",
                context=context
            )

            # Generate clarification response
            logger.debug("[run_clarification] Sending clarification request to LLM")
            response = await self.llm.ainvoke([HumanMessage(content=formatted_prompt)])
            clarification_content = response.content.strip()

            logger.info(f"[run_clarification] Generated clarification of {len(clarification_content)} chars")

            return {
                "content": clarification_content,
                "language": language,
                "response_mode": response_mode,
                "type": "clarification",
                "target_phrase": selected_text,
                "sources": [],
                "metadata": {
                    "clarification_type": "explanation",
                    "selected_text": selected_text,
                    "original_message_preview": previous_message[:100] + "..." if len(previous_message) > 100 else previous_message
                }
            }

        except Exception as e:
            logger.error(f"[run_clarification] Error generating clarification: {e}")
            return {
                "content": f"I apologize, but I couldn't generate a clarification for '{selected_text}'. Please try rephrasing your question or ask me directly about this topic.",
                "language": language,
                "response_mode": response_mode,
                "type": "clarification",
                "target_phrase": selected_text,
                "sources": [],
                "error": {"type": "ClarificationError", "message": str(e)},
                "metadata": {
                    "clarification_type": "error",
                    "selected_text": selected_text
                }
            }

    async def generate_followup_question(
        self,
        selected_text: str,
        context: str,
        language: str = "en"
    ) -> Dict[str, Any]:
        """
        Generate a follow-up question based on selected text from assistant message.

        Args:
            selected_text (str): The text the user selected
            context (str): Full context including assistant message and previous user query
            language (str): Response language

        Returns:
            Dict[str, Any]: Generated follow-up question with metadata
        """
        logger.debug(f"[generate_followup_question] Started for selected_text: '{selected_text[:50]}...'")

        try:
            # Create follow-up question prompt
            prompt = f"""Given this selected snippet from a chatbot's response, generate a follow-up question a user might naturally ask to clarify or explore this point further. Return only the follow-up question.

Context:
{context}

Selected text: "{selected_text}"

Generate a natural follow-up question:"""

            # Generate follow-up question
            logger.debug("[generate_followup_question] Sending follow-up generation request to LLM")
            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            followup_question = response.content.strip()

            # Remove any quotes or formatting that might be added by the LLM
            followup_question = followup_question.strip('"').strip("'").strip()

            logger.info(f"[generate_followup_question] Generated follow-up question: {followup_question}")

            return {
                "content": followup_question,
                "language": language,
                "type": "followup_question",
                "selected_text": selected_text,
                "metadata": {
                    "question_type": "clarification",
                    "selected_text": selected_text,
                    "context_preview": context[:200] + "..." if len(context) > 200 else context
                }
            }

        except Exception as e:
            logger.error(f"[generate_followup_question] Error generating follow-up question: {e}")
            return {
                "content": f"Can you explain more about: {selected_text}?",
                "language": language,
                "type": "followup_question",
                "selected_text": selected_text,
                "error": {"type": "FollowupGenerationError", "message": str(e)},
                "metadata": {
                    "question_type": "fallback",
                    "selected_text": selected_text
                }
            }


# Add main block for standalone testing
if __name__ == "__main__":
    async def test():
        cb = ChainBuilder()
        result = await cb.run_chain("tell me about moonlighting policy?", device_id="test-device-id")
        print("\n\n=== Chain Output ===")
        for k, v in result.items():
            print(f"{k}: {v}\n")

    asyncio.run(test())